from flask import Blueprint, request, jsonify, Response, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from functools import wraps
import json
from datetime import datetime, timedelta

from ..models.models import Department, Position, Nurse, Schedule, ScheduleAssignment, LLMConfig, User, db
from ..services.auth_service import AuthService
from ..services.scheduling_service import SchedulingService
from ..services.rule_based_scheduling_service import RuleBasedSchedulingService
from ..services.validation_service import ValidationService
from ..services.llm_service import LLMService
from flask import current_app

# 导入已创建的蓝图
from . import bp

def token_required(f):
    """装饰器：验证JWT token"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]
            except IndexError:
                return jsonify({
                    'code': 401,
                    'message': 'Token格式错误',
                    'data': None
                }), 401
        
        if not token:
            return jsonify({
                'code': 401,
                'message': '缺少Token',
                'data': None
            }), 401
        
        user_id = AuthService.verify_token(token)
        if not user_id:
            return jsonify({
                'code': 401,
                'message': 'Token无效或已过期',
                'data': None
            }), 401
        
        current_user = User.query.get(user_id)
        return f(current_user, *args, **kwargs)
    
    return decorated

@bp.route('/docs')
def docs():
    """API文档"""
    return jsonify({
        "message": "护士排班系统API文档",
        "version": "1.0.0",
        "endpoints": {
            "POST /auth/register": "用户注册",
            "POST /auth/login": "用户登录",
            "GET /departments": "获取所有科室",
            "POST /departments": "创建科室",
            "GET /positions": "获取所有岗位",
            "POST /positions": "创建岗位",
            "GET /nurses": "获取所有护士",
            "POST /nurses": "创建护士",
            "PUT /nurses/<id>/positions": "更新护士岗位",
            "GET /nurses/<id>/positions": "获取护士岗位胜任能力",
            "GET /schedules": "获取所有排班表",
            "POST /schedules": "创建排班表",
                "POST /schedules/generate/rule-based": "使用规则生成排班表",
            "GET /schedules/<id>/statistics": "获取排班表统计信息",
            "GET /llm-configs": "获取LLM配置",
            "POST /llm-configs": "创建LLM配置"
        }
    })

@bp.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        role = data.get('role', 'user')
        
        # 验证必需字段
        required_fields = ['username', 'email', 'password']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            return jsonify({
                'code': 400,
                'message': f'缺少必需字段: {", ".join(missing)}',
                'data': None
            }), 400
        
        # 验证邮箱格式
        if not ValidationService.validate_email(email):
            return jsonify({
                'code': 400,
                'message': '邮箱格式不正确',
                'data': None
            }), 400
        
        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({
                'code': 400,
                'message': '用户名已存在',
                'data': None
            }), 400
        
        if User.query.filter_by(email=email).first():
            return jsonify({
                'code': 400,
                'message': '邮箱已存在',
                'data': None
            }), 400
        
        # 创建用户
        user = AuthService.create_user(username, email, password, role)
        
        return jsonify({
            'code': 200,
            'message': '注册成功',
            'data': user.to_dict()
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': str(e),
            'data': None
        }), 500

@bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # 验证必需字段
        required_fields = ['username', 'password']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            return jsonify({
                'code': 400,
                'message': f'缺少必需字段: {", ".join(missing)}',
                'data': None
            }), 400
        
        user, token = AuthService.authenticate_user(username, password)
        if not user:
            return jsonify({
                'code': 401,
                'message': '用户名或密码错误',
                'data': None
            }), 401
        
        return jsonify({
            'code': 200,
            'message': '登录成功',
            'data': {
                'user': user.to_dict(),
                'token': token
            }
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': str(e),
            'data': None
        }), 500

@bp.route('/departments', methods=['GET'])
def get_departments():
    """获取所有科室"""
    departments = Department.query.all()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": [dept.to_dict() for dept in departments]
    })

@bp.route('/departments', methods=['POST'])
@token_required
def create_department(current_user):
    """创建科室"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    data = request.get_json()
    
    # 验证数据
    errors = ValidationService.validate_department_data(data)
    if errors:
        return jsonify({
            'code': 400,
            'message': '数据验证失败',
            'data': {
                'errors': errors
            }
        }), 400
    
    department = Department(
        name=data.get('name'),
        description=data.get('description')
    )
    from backend.app.models.models import db
    db.session.add(department)
    db.session.commit()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": department.to_dict()
    })

@bp.route('/departments/<int:department_id>', methods=['PUT'])
@token_required
def update_department(current_user, department_id):
    """更新科室"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    department = Department.query.get_or_404(department_id)
    data = request.get_json()
    
    # 验证数据
    errors = ValidationService.validate_department_data(data)
    if errors:
        return jsonify({
            'code': 400,
            'message': '数据验证失败',
            'data': {
                'errors': errors
            }
        }), 400
    
    department.name = data.get('name', department.name)
    department.description = data.get('description', department.description)
    db.session.commit()
    
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": department.to_dict()
    })

@bp.route('/departments/<int:department_id>', methods=['DELETE'])
@token_required
def delete_department(current_user, department_id):
    """删除科室"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    department = Department.query.get_or_404(department_id)
    
    # 检查是否有关联的护士或排班表
    if department.nurses:
        return jsonify({
            'code': 400,
            'message': '该科室下有关联的护士，无法删除',
            'data': None
        }), 400
    
    if department.schedules:
        return jsonify({
            'code': 400,
            'message': '该科室下有关联的排班表，无法删除',
            'data': None
        }), 400
    
    db.session.delete(department)
    db.session.commit()
    
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": None
    })

@bp.route('/positions', methods=['GET'])
def get_positions():
    """获取所有岗位"""
    positions = Position.query.all()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": [pos.to_dict() for pos in positions]
    })

@bp.route('/positions', methods=['POST'])
@token_required
def create_position(current_user):
    """创建岗位"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    data = request.get_json()
    
    # 验证必需字段
    required_fields = ['name']
    missing = ValidationService.validate_required_fields(data, required_fields)
    if missing:
        return jsonify({
            'code': 400,
            'message': f'缺少必需字段: {", ".join(missing)}',
            'data': None
        }), 400
    
    position = Position(
        name=data.get('name'),
        description=data.get('description')
    )
    db.session.add(position)
    db.session.commit()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": position.to_dict()
    })

@bp.route('/positions/<int:position_id>', methods=['PUT'])
@token_required
def update_position(current_user, position_id):
    """更新岗位信息"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            "code": 403,
            "message": "权限不足",
            "data": None
        }), 403

    try:
        position = Position.query.get_or_404(position_id)
        data = request.get_json()

        # 更新岗位信息
        if 'name' in data:
            position.name = data['name']
        if 'description' in data:
            position.description = data['description']

        db.session.commit()

        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": position.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "code": 500,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/positions/<int:position_id>', methods=['DELETE'])
@token_required
def delete_position(current_user, position_id):
    """删除岗位"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            "code": 403,
            "message": "权限不足",
            "data": None
        }), 403

    try:
        position = Position.query.get_or_404(position_id)

        # 检查是否有关联的排班记录
        from ..models.models import ScheduleAssignment
        assignments = ScheduleAssignment.query.filter_by(position_id=position_id).first()
        if assignments:
            return jsonify({
                "code": 400,
                "message": "无法删除：该岗位有关联的排班记录",
                "data": None
            }), 400

        db.session.delete(position)
        db.session.commit()

        return jsonify({
            "code": 200,
            "message": "删除成功",
            "data": None
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "code": 500,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/nurses', methods=['GET'])
def get_nurses():
    """获取所有护士"""
    nurses = Nurse.query.all()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": [nurse.to_dict() for nurse in nurses]
    })

@bp.route('/nurses', methods=['POST'])
@token_required
def create_nurse(current_user):
    """创建护士"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    data = request.get_json()
    
    # 验证数据
    errors = ValidationService.validate_nurse_data(data)
    if errors:
        return jsonify({
            'code': 400,
            'message': '数据验证失败',
            'data': {
                'errors': errors
            }
        }), 400
    
    nurse = Nurse(
        name=data.get('name'),
        employee_id=data.get('employee_id'),
        department_id=data.get('department_id')
    )
    
    # 处理岗位关联
    position_ids = data.get('position_ids', [])
    if position_ids:
        positions = Position.query.filter(Position.id.in_(position_ids)).all()
        nurse.positions = positions
    
    db.session.add(nurse)
    db.session.commit()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": nurse.to_dict()
    })

@bp.route('/nurses/<int:nurse_id>', methods=['PUT'])
@token_required
def update_nurse(current_user, nurse_id):
    """更新护士信息"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            "code": 403,
            "message": "权限不足",
            "data": None
        }), 403

    try:
        nurse = Nurse.query.get_or_404(nurse_id)
        data = request.get_json()

        # 更新护士信息
        if 'name' in data:
            nurse.name = data['name']
        if 'employee_id' in data:
            nurse.employee_id = data['employee_id']
        if 'department_id' in data:
            nurse.department_id = data['department_id']
        if 'phone' in data:
            nurse.phone = data['phone']
        if 'email' in data:
            nurse.email = data['email']
        if 'is_active' in data:
            nurse.is_active = data['is_active']

        db.session.commit()

        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": nurse.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "code": 500,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/nurses/<int:nurse_id>', methods=['DELETE'])
@token_required
def delete_nurse(current_user, nurse_id):
    """删除护士"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            "code": 403,
            "message": "权限不足",
            "data": None
        }), 403

    try:
        nurse = Nurse.query.get_or_404(nurse_id)

        # 检查是否有关联的排班记录
        from ..models.models import ScheduleAssignment
        assignments = ScheduleAssignment.query.filter_by(nurse_id=nurse_id).first()
        if assignments:
            return jsonify({
                "code": 400,
                "message": "无法删除：该护士有关联的排班记录",
                "data": None
            }), 400

        db.session.delete(nurse)
        db.session.commit()

        return jsonify({
            "code": 200,
            "message": "删除成功",
            "data": None
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "code": 500,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/nurses/<int:nurse_id>/positions', methods=['GET'])
def get_nurse_positions(nurse_id):
    """获取护士岗位胜任能力"""
    try:
        scheduling_service = SchedulingService()
        result = scheduling_service.get_nurse_position_compatibility(nurse_id)
        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": result
        })
    except ValueError as e:
        return jsonify({
            "code": 404,
            "message": str(e),
            "data": None
        }), 404

@bp.route('/nurses/<int:nurse_id>/positions', methods=['PUT'])
@token_required
def update_nurse_positions(current_user, nurse_id):
    """更新护士岗位"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    try:
        data = request.get_json()
        position_ids = data.get('position_ids', [])
        
        scheduling_service = SchedulingService()
        result = scheduling_service.update_nurse_positions(nurse_id, position_ids)
        
        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": result
        })
    except ValueError as e:
        return jsonify({
            "code": 404,
            "message": str(e),
            "data": None
        }), 404

@bp.route('/schedules', methods=['GET'])
def get_schedules():
    """获取所有排班表"""
    schedules = Schedule.query.all()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": [schedule.to_dict() for schedule in schedules]
    })

@bp.route('/schedules/<int:schedule_id>', methods=['DELETE'])
@token_required
def delete_schedule(current_user, schedule_id):
    """删除排班表"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    schedule = Schedule.query.get_or_404(schedule_id)
    
    # 删除排班表及其相关的排班分配
    db.session.delete(schedule)
    db.session.commit()
    
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": None
    })

@bp.route('/schedules/<int:schedule_id>/details', methods=['GET'])
def get_schedule_details(schedule_id):
    """获取排班表详情"""
    # 获取排班表
    schedule = Schedule.query.get(schedule_id)
    if not schedule:
        return jsonify({
            'code': 404,
            'message': '排班表不存在',
            'data': None
        }), 404
    
    # 获取排班分配详情
    assignments = ScheduleAssignment.query.filter_by(schedule_id=schedule_id).all()
    
    # 构建按护士分组的排班详情
    nurse_schedules = {}
    # 获取所有涉及的护士
    nurse_ids = list(set([assignment.nurse_id for assignment in assignments]))
    nurses = Nurse.query.filter(Nurse.id.in_(nurse_ids)).all()
    nurse_map = {nurse.id: nurse for nurse in nurses}
    
    # 获取所有涉及的岗位
    position_ids = list(set([assignment.position_id for assignment in assignments]))
    positions = Position.query.filter(Position.id.in_(position_ids)).all()
    position_map = {position.id: position for position in positions}
    
    # 按护士分组整理排班数据
    for assignment in assignments:
        nurse_id = assignment.nurse_id
        if nurse_id not in nurse_schedules:
            nurse = nurse_map.get(nurse_id)
            if not nurse:
                continue
            nurse_schedules[nurse_id] = {
                'nurse_id': nurse_id,
                'nurse_name': nurse.name,
                'assignments': {}
            }
        
        # 将日期作为键，岗位和班次作为值
        date_str = assignment.date.strftime('%Y-%m-%d')
        position = position_map.get(assignment.position_id)
        position_name = position.name if position else '未知岗位'
        nurse_schedules[nurse_id]['assignments'][date_str] = {
            'position': position_name,
            'shift': assignment.shift or '未知班次'
        }
    
    # 转换为按日期排序的列表
    schedule_dates = []
    current_date = schedule.start_date
    while current_date <= schedule.end_date:
        schedule_dates.append(current_date.strftime('%Y-%m-%d'))
        current_date += timedelta(days=1)
    
    # 构建返回数据
    result = {
        'schedule': schedule.to_dict(),
        'schedule_dates': schedule_dates,
        'nurse_schedules': list(nurse_schedules.values())
    }
    
    return jsonify({
        'code': 200,
        'message': '操作成功',
        'data': result
    })

@bp.route('/schedules', methods=['POST'])
@token_required
def create_schedule(current_user):
    """创建排班表"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    data = request.get_json()
    
    # 验证数据
    errors = ValidationService.validate_schedule_data(data)
    if errors:
        return jsonify({
            'code': 400,
            'message': '数据验证失败',
            'data': {
                'errors': errors
            }
        }), 400
    
    schedule = Schedule(
        name=data.get('name'),
        department_id=data.get('department_id'),
        start_date=data.get('start_date'),
        end_date=data.get('end_date'),
        description=data.get('description')
    )
    db.session.add(schedule)
    db.session.commit()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": schedule.to_dict()
    })

@bp.route('/schedules/generate', methods=['POST'])
@token_required
def generate_schedule(current_user):
    """使用LLM生成排班表（支持流式和非流式输出）"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['department_id', 'requirements', 'schedule_name', 'start_date', 'end_date']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            return jsonify({
                'code': 400,
                'message': f'缺少必需字段: {", ".join(missing)}',
                'data': None
            }), 400
        
        # 验证日期格式
        if not ValidationService.validate_date_format(data['start_date']):
            return jsonify({
                'code': 400,
                'message': '开始日期格式不正确，应为YYYY-MM-DD',
                'data': None
            }), 400
        
        if not ValidationService.validate_date_format(data['end_date']):
            return jsonify({
                'code': 400,
                'message': '结束日期格式不正确，应为YYYY-MM-DD',
                'data': None
            }), 400
        
        # 验证日期范围
        if not ValidationService.validate_date_range(data['start_date'], data['end_date']):
            return jsonify({
                'code': 400,
                'message': '开始日期不能晚于结束日期',
                'data': None
            }), 400
        
        department_id = data.get('department_id')
        requirements = data.get('requirements')
        schedule_name = data.get('schedule_name')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        llm_config_id = data.get('llm_config_id')
        stream = data.get('stream', True)  # 新增参数，是否使用流式输出，默认为True
        
        # 获取科室信息
        department = Department.query.get(department_id)
        if not department:
            return jsonify({
                'code': 404,
                'message': '科室不存在',
                'data': None
            }), 404

        # 获取科室护士列表
        nurses = Nurse.query.filter_by(department_id=department_id, is_active=True).all()
        
        # 检查科室是否有护士
        if not nurses:
            return jsonify({
                'code': 400,
                'message': f'科室 "{department.name}" 没有可用的护士，请先为该科室添加护士',
                'data': None
            }), 400
        
        # 获取所有岗位
        positions = Position.query.all()
        
        # 调用LLM服务生成排班表
        llm_service = LLMService(llm_config_id)
        
        if stream:  # 根据stream参数决定使用哪种方式
            # 流式输出
            response = llm_service.generate_schedule_stream(
                requirements, nurses, positions, department, start_date, end_date)
            
            def generate_stream():
                full_response = ""
                try:
                    for chunk in response.iter_lines():
                        if chunk:
                            decoded_chunk = chunk.decode('utf-8')
                            yield f"data: {decoded_chunk}\n\n"
                            
                            # 收集完整的响应内容用于后续处理
                            if decoded_chunk.startswith('data: '):
                                data_content = decoded_chunk[6:]  # 移除 'data: ' 前缀
                                if data_content != '[DONE]':
                                    try:
                                        parsed = json.loads(data_content)
                                        if 'choices' in parsed and parsed['choices']:
                                            delta = parsed['choices'][0].get('delta', {})
                                            content = delta.get('content', '')
                                            if content:
                                                full_response += content
                                    except json.JSONDecodeError:
                                        # 如果不是JSON格式，跳过
                                        pass
                    
                    # 解析完整的响应并保存排班表
                    try:
                        # 清理响应内容，提取JSON部分
                        cleaned_response = full_response.strip()
                        if cleaned_response.startswith("```json"):
                            cleaned_response = cleaned_response[7:]  # 移除开头的 ```json
                        if cleaned_response.endswith("```"):
                            cleaned_response = cleaned_response[:-3]  # 移除结尾的 ```
                        
                        # 解析JSON
                        schedule_result = json.loads(cleaned_response)
                        schedule_data = schedule_result.get('schedule', [])
                        
                        # 保存排班表到数据库
                        schedule = Schedule(
                            name=schedule_name,
                            department_id=department_id,
                            start_date=datetime.strptime(start_date, '%Y-%m-%d'),
                            end_date=datetime.strptime(end_date, '%Y-%m-%d'),
                            created_by=current_user.id
                        )
                        db.session.add(schedule)
                        db.session.flush()  # 获取schedule.id但不提交事务
                        
                        # 创建排班分配记录
                        for item in schedule_data:
                            assignment = ScheduleAssignment(
                                schedule_id=schedule.id,
                                date=datetime.strptime(item['date'], '%Y-%m-%d').date(),
                                shift=item['shift'],
                                nurse_name=item['nurse_name'],
                                position_name=item['position']
                            )
                            db.session.add(assignment)
                        
                        db.session.commit()
                        
                        # 发送保存成功的消息
                        success_message = json.dumps({
                            "type": "success",
                            "message": "排班表已成功保存到数据库"
                        })
                        yield f"data: {success_message}\n\n"
                    except Exception as e:
                        # 发送保存失败的消息
                        error_message = json.dumps({
                            "type": "error",
                            "message": f"保存排班表时出错: {str(e)}"
                        })
                        yield f"data: {error_message}\n\n"
                        db.session.rollback()
                    
                    yield "data: [DONE]\n\n"
                except Exception as e:
                    import json
                    error_data = json.dumps({'error': str(e)})
                    yield f"data: {error_data}\n\n"
            
            # 返回流式响应
            return Response(
                generate_stream(),
                content_type='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:  # 非流式输出
            # 调用LLM服务生成排班表（非流式输出）
            prompt = llm_service._build_schedule_prompt(
                requirements, nurses, positions, department, start_date, end_date)
            
            headers = {
                "Authorization": f"Bearer {llm_service.api_key}",
                "Content-Type": "application/json"
            }
            
            llm_data = {
                "model": llm_service.model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的护士排班助手，请根据提供的信息生成合理的排班表。严格按照要求输出标准JSON格式。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 4000,
                "stream": False  # 非流式输出
            }
            
            response = requests.post(
                f"{llm_service.api_base}/chat/completions",
                headers=headers,
                json=llm_data
            )
            
            if response.status_code != 200:
                raise Exception(f"调用LLM API失败: {response.text}")
            
            # 解析响应
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # 清理响应内容，提取JSON部分
            cleaned_content = content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content[7:]  # 移除开头的 ```json
            if cleaned_content.endswith("```"):
                cleaned_content = cleaned_content[:-3]  # 移除结尾的 ```
            
            # 解析JSON
            schedule_result = json.loads(cleaned_content)
            schedule_data = schedule_result.get('schedule', [])
            
            # 保存排班表到数据库
            schedule = Schedule(
                name=schedule_name,
                department_id=department_id,
                start_date=datetime.strptime(start_date, '%Y-%m-%d'),
                end_date=datetime.strptime(end_date, '%Y-%m-%d'),
                created_by=current_user.id
            )
            db.session.add(schedule)
            db.session.flush()  # 获取schedule.id但不提交事务
            
            # 创建排班分配记录
            for item in schedule_data:
                assignment = ScheduleAssignment(
                    schedule_id=schedule.id,
                    date=datetime.strptime(item['date'], '%Y-%m-%d').date(),
                    shift=item['shift'],
                    nurse_name=item['nurse_name'],
                    position_name=item['position']
                )
                db.session.add(assignment)
            
            db.session.commit()
            
            return jsonify({
                "code": 200,
                "message": "排班表生成并保存成功",
                "data": {
                    "schedule": schedule.to_dict(),
                    "schedule_data": schedule_data
                }
            })
            
    except Exception as e:
        import traceback
        current_app.logger.error(f"流式排班生成失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

# 此路由已被合并到/schedules/generate中，作为非流式输出选项，所以可以删除这个单独的路由

@bp.route('/schedules/generate-stream', methods=['POST'])
@token_required
def generate_schedule_stream(current_user):
    """使用LLM生成排班表（流式输出版本）"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['department_id', 'requirements', 'schedule_name', 'start_date', 'end_date']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            return jsonify({
                'code': 400,
                'message': f'缺少必需字段: {", ".join(missing)}',
                'data': None
            }), 400
        
        # 验证日期格式
        if not ValidationService.validate_date_format(data['start_date']):
            return jsonify({
                'code': 400,
                'message': '开始日期格式不正确，应为YYYY-MM-DD',
                'data': None
            }), 400
        
        if not ValidationService.validate_date_format(data['end_date']):
            return jsonify({
                'code': 400,
                'message': '结束日期格式不正确，应为YYYY-MM-DD',
                'data': None
            }), 400
        
        # 验证日期范围
        if not ValidationService.validate_date_range(data['start_date'], data['end_date']):
            return jsonify({
                'code': 400,
                'message': '开始日期不能晚于结束日期',
                'data': None
            }), 400
        
        department_id = data.get('department_id')
        requirements = data.get('requirements')
        schedule_name = data.get('schedule_name')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        model_id = data.get('model_id')  # 新增模型ID参数
        
        # 获取科室信息
        department = Department.query.get(department_id)
        if not department:
            return jsonify({
                'code': 404,
                'message': '科室不存在',
                'data': None
            }), 404

        # 获取科室护士列表
        nurses = Nurse.query.filter_by(department_id=department_id, is_active=True).all()
        
        # 检查科室是否有护士
        if not nurses:
            return jsonify({
                'code': 400,
                'message': f'科室 "{department.name}" 没有可用的护士，请先为该科室添加护士',
                'data': None
            }), 400
        
        # 获取所有岗位
        positions = Position.query.all()
        
        # 调用LLM服务生成排班表（流式输出）
        llm_service = LLMService(model_id)
        response = llm_service.generate_schedule_stream(
            requirements, nurses, positions, department, start_date, end_date)

        # 移除app实例获取，简化流程

        # 真正的实时流式处理
        def generate_stream():
            import json
            full_response = ""

            try:
                # 实时处理LLM响应流
                for chunk in response:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        full_response += content

                        # 立即发送给前端
                        chunk_data = {
                            "choices": [{
                                "delta": {
                                    "content": content
                                }
                            }]
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # LLM响应完成，分析并发送完成消息
                try:
                    # 分析排班结果，不使用数据库查询
                    analysis_result = analyze_schedule_result_simple(full_response, nurses, positions)

                    completion_message = json.dumps({
                        "type": "completion",
                        "message": "排班表生成完成",
                        "full_response": full_response,
                        "analysis": analysis_result
                    })
                    yield f"data: {completion_message}\n\n"
                except Exception as e:
                    # 如果分析失败，仍然发送完成消息
                    import traceback
                    print(f"[ERROR] 分析排班结果失败: {str(e)}")
                    print(traceback.format_exc())
                    completion_message = json.dumps({
                        "type": "completion",
                        "message": "排班表生成完成",
                        "full_response": full_response,
                        "analysis_error": str(e)
                    })
                    yield f"data: {completion_message}\n\n"

            except Exception as e:
                error_message = json.dumps({
                    "type": "error",
                    "message": f"LLM生成失败: {str(e)}"
                })
                yield f"data: {error_message}\n\n"

            yield "data: [DONE]\n\n"
        
        # 返回流式响应
        return Response(
            generate_stream(),
            content_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )
        
    except Exception as e:
        import traceback
        print(f"流式排班生成失败: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500


def analyze_schedule_result_simple(full_response, existing_nurses, existing_positions):
    """简化的分析排班结果函数，不依赖数据库查询"""
    import json
    import re

    try:
        # 清理响应内容，提取JSON部分
        cleaned_response = full_response.strip()
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:]
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]

        # 进一步清理，移除可能的多余空白和换行
        cleaned_response = cleaned_response.strip()

        # 解析JSON
        schedule_result = json.loads(cleaned_response)
        schedule_data = schedule_result.get('schedule', [])

        if not schedule_data:
            return {"new_nurses": [], "new_positions": [], "message": "未找到排班数据"}

        # 获取系统中现有的护士和岗位名称
        existing_nurse_names = set()
        existing_position_names = set()

        # 从传入的护士对象中提取名称
        for nurse in existing_nurses:
            existing_nurse_names.add(nurse.name)

        # 从传入的岗位对象中提取名称
        for position in existing_positions:
            existing_position_names.add(position.name)
            if hasattr(position, 'short_name') and position.short_name:
                existing_position_names.add(position.short_name)

        # 分析排班数据中的护士和岗位
        found_nurses = set()
        found_positions = set()

        for item in schedule_data:
            if 'nurse_name' in item:
                found_nurses.add(item['nurse_name'])
            if 'position' in item:
                found_positions.add(item['position'])

        # 识别新的护士和岗位
        new_nurses = list(found_nurses - existing_nurse_names)
        new_positions = list(found_positions - existing_position_names)

        # 构建分析结果
        analysis = {
            "new_nurses": new_nurses,
            "new_positions": new_positions,
            "total_nurses_found": len(found_nurses),
            "total_positions_found": len(found_positions),
            "existing_nurses_count": len(existing_nurse_names),
            "existing_positions_count": len(existing_position_names)
        }

        # 添加提示消息
        messages = []
        if new_nurses:
            messages.append(f"发现 {len(new_nurses)} 个新护士：{', '.join(new_nurses)}")
        if new_positions:
            messages.append(f"发现 {len(new_positions)} 个新岗位：{', '.join(new_positions)}")

        if not new_nurses and not new_positions:
            messages.append("所有护士和岗位都已在系统中")

        analysis["messages"] = messages

        return analysis

    except Exception as e:
        return {
            "error": f"分析排班结果失败: {str(e)}",
            "new_nurses": [],
            "new_positions": []
        }

@bp.route('/schedules/add-missing-data', methods=['POST'])
@token_required
def add_missing_data(current_user):
    """添加缺失的护士和岗位数据"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({
            "code": 403,
            "message": "权限不足",
            "data": None
        }), 403

    try:
        data = request.get_json()
        new_nurses = data.get('new_nurses', [])
        new_positions = data.get('new_positions', [])
        department_id = data.get('department_id', 1)  # 默认部门ID

        added_nurses = []
        added_positions = []

        # 添加新护士
        for nurse_name in new_nurses:
            # 检查护士是否已存在
            existing_nurse = Nurse.query.filter_by(name=nurse_name).first()
            if not existing_nurse:
                # 生成工号
                nurse_count = Nurse.query.count()
                employee_id = f"N{nurse_count + 1:03d}"

                new_nurse = Nurse(
                    name=nurse_name,
                    employee_id=employee_id,
                    department_id=department_id,
                    is_active=True
                )
                db.session.add(new_nurse)
                added_nurses.append({
                    "name": nurse_name,
                    "employee_id": employee_id
                })

        # 添加新岗位
        for position_name in new_positions:
            # 检查岗位是否已存在
            existing_position = Position.query.filter_by(name=position_name).first()
            if not existing_position:
                # 智能推断岗位属性
                position_info = infer_position_attributes(position_name)

                new_position = Position(
                    name=position_name,
                    short_name=position_info['short_name'],
                    icon=position_info['icon'],
                    color=position_info['color'],
                    area=position_info['area'],
                    description=f"AI识别添加的岗位：{position_name}"
                )
                db.session.add(new_position)
                added_positions.append({
                    "name": position_name,
                    "short_name": position_info['short_name'],
                    "icon": position_info['icon'],
                    "color": position_info['color'],
                    "area": position_info['area']
                })

        db.session.commit()

        return jsonify({
            "code": 200,
            "message": "数据添加成功",
            "data": {
                "added_nurses": added_nurses,
                "added_positions": added_positions,
                "nurses_count": len(added_nurses),
                "positions_count": len(added_positions)
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            "code": 500,
            "message": f"添加数据失败: {str(e)}",
            "data": None
        }), 500

def infer_position_attributes(position_name):
    """智能推断岗位属性"""
    # 岗位关键词映射
    position_keywords = {
        '责任': {'short_name': '责', 'icon': 'el-icon-user-solid', 'color': '#409EFF', 'area': '通用'},
        '值班': {'short_name': '值', 'icon': 'el-icon-time', 'color': '#F56C6C', 'area': '通用'},
        '夜班': {'short_name': '夜', 'icon': 'el-icon-moon', 'color': '#303133', 'area': '通用'},
        '治疗': {'short_name': '治', 'icon': 'el-icon-first-aid-kit', 'color': '#909399', 'area': '通用'},
        '听班': {'short_name': '听', 'icon': 'el-icon-phone', 'color': '#C0C4CC', 'area': '通用'},
        '辅助': {'short_name': '辅', 'icon': 'el-icon-help', 'color': '#E6A23C', 'area': '通用'},
        '管理': {'short_name': '管', 'icon': 'el-icon-s-custom', 'color': '#722ED1', 'area': '通用'},
        '休': {'short_name': '休', 'icon': 'el-icon-coffee', 'color': '#52C41A', 'area': '通用'}
    }

    # 默认属性
    default_attrs = {
        'short_name': position_name[:2] if len(position_name) >= 2 else position_name,
        'icon': 'el-icon-user',
        'color': '#606266',
        'area': '通用'
    }

    # 根据关键词匹配
    for keyword, attrs in position_keywords.items():
        if keyword in position_name:
            result = default_attrs.copy()
            result.update(attrs)
            return result

    return default_attrs

@bp.route('/schedules/generate/rule-based', methods=['POST'])
@token_required
def generate_schedule_rule_based(current_user):
    """
    基于规则生成排班表
    """
    try:
        data = request.get_json()
        department_id = data.get('department_id')
        
        # 初始化日期变量
        next_monday = None
        
        # 如果没有提供排班时间，则使用默认的下周时间（周一到周日）
        if not data.get('start_date') or not data.get('end_date'):
            # 计算下周周一的日期
            today = datetime.now().date()
            days_ahead = 0 - today.weekday() + 7  # 下周一
            if days_ahead <= 0:  # 如果今天是周一，则下周一为7天后
                days_ahead += 7
            next_monday = today + timedelta(days=days_ahead)
            next_sunday = next_monday + timedelta(days=6)
            
            start_date = next_monday.strftime('%Y-%m-%d')
            end_date = next_sunday.strftime('%Y-%m-%d')
        else:
            start_date = data.get('start_date')
            end_date = data.get('end_date')

        # 如果没有提供排班名称，则使用默认名称格式：YYYY年第WW周
        if not data.get('schedule_name'):
            # 计算下周的年份和周次
            if not data.get('start_date') or not data.get('end_date'):
                # 使用我们计算的下周日期
                if next_monday is not None:
                    next_week_date = next_monday
                else:
                    # 重新计算下周日期
                    today = datetime.now().date()
                    days_ahead = 0 - today.weekday() + 7  # 下周一
                    if days_ahead <= 0:  # 如果今天是周一，则下周一为7天后
                        days_ahead += 7
                    next_week_date = today + timedelta(days=days_ahead)
            else:
                # 使用提供的日期
                next_week_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            
            year = next_week_date.isocalendar()[0]
            week_number = next_week_date.isocalendar()[1]
            schedule_name = f"{year}年第{week_number}周"
        else:
            schedule_name = data.get('schedule_name')

        # 验证参数
        if not department_id:
            return jsonify({
                'code': 400,
                'message': '缺少必要参数',
                'data': None
            }), 400

        # 验证日期格式
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                'code': 400,
                'message': '日期格式错误，应为YYYY-MM-DD',
                'data': None
            }), 400

        # 检查科室是否存在
        department = Department.query.get(department_id)
        if not department:
            return jsonify({
                'code': 404,
                'message': '科室不存在',
                'data': None
            }), 404

        # 检查同一周内是否已存在排班表
        existing_schedules = Schedule.query.filter(
            Schedule.department_id == department_id,
            Schedule.start_date <= start_date_obj,
            Schedule.end_date >= end_date_obj
        ).all()

        if existing_schedules:
            return jsonify({
                'code': 409,  # Conflict
                'message': '同一周内已存在排班表',
                'data': {
                    'existing_schedules': [s.to_dict() for s in existing_schedules]
                }
            }), 409

        # 使用基于规则的排班服务生成排班表
        rule_service = RuleBasedSchedulingService()
        schedule_data = rule_service.generate_schedule(department_id, start_date, end_date)

        # 创建排班表记录
        scheduling_service = SchedulingService()
        schedule = scheduling_service.create_schedule_from_data(
            department_id, schedule_name, start_date, end_date, schedule_data
        )

        return jsonify({
            'code': 200,
            'message': '排班表生成成功',
            'data': {
                'schedule': schedule.to_dict(),
                'schedule_data': schedule_data
            }
        })

    except Exception as e:
        current_app.logger.exception("基于规则生成排班表时出错")
        return jsonify({
            'code': 500,
            'message': f'生成排班表失败: {str(e)}',
            'data': None
        }), 500

@bp.route('/llm-configs', methods=['GET'])
def get_llm_configs():
    """获取所有LLM配置"""
    configs = LLMConfig.query.all()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": [config.to_dict() for config in configs]
    })

@bp.route('/llm-configs', methods=['POST'])
@token_required
def create_llm_config(current_user):
    """创建LLM配置"""
    if current_user.role not in ['admin']:
        return jsonify({
            'code': 403,
            'message': '权限不足',
            'data': None
        }), 403
        
    data = request.get_json()
    
    # 验证必需字段
    required_fields = ['name', 'api_base', 'api_key', 'model']
    missing = ValidationService.validate_required_fields(data, required_fields)
    if missing:
        return jsonify({
            'code': 400,
            'message': f'缺少必需字段: {", ".join(missing)}',
            'data': None
        }), 400
    
    config = LLMConfig(
        name=data.get('name'),
        api_base=data.get('api_base'),
        api_key=data.get('api_key'),
        model=data.get('model')
    )
    db.session.add(config)
    db.session.commit()
    return jsonify({
        "code": 200,
        "message": "操作成功",
        "data": config.to_dict()
    })

# ==================== 模型配置管理 ====================

@bp.route('/models', methods=['GET'])
@token_required
def get_models(current_user):
    """获取所有模型配置"""
    from backend.app.services.model_config_service import ModelConfigService

    try:
        models = ModelConfigService.get_all_models()
        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": models
        })
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"获取模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models/enabled', methods=['GET'])
@token_required
def get_enabled_models(current_user):
    """获取所有启用的模型配置"""
    from backend.app.services.model_config_service import ModelConfigService

    try:
        models = ModelConfigService.get_enabled_models()
        return jsonify({
            "code": 200,
            "message": "操作成功",
            "data": models
        })
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"获取启用模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models/default', methods=['GET'])
@token_required
def get_default_model(current_user):
    """获取默认模型配置"""
    from backend.app.services.model_config_service import ModelConfigService

    try:
        model = ModelConfigService.get_default_model()
        if model:
            return jsonify({
                "code": 200,
                "message": "操作成功",
                "data": model
            })
        else:
            return jsonify({
                "code": 404,
                "message": "未找到默认模型配置",
                "data": None
            }), 404
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"获取默认模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models', methods=['POST'])
@token_required
def create_model(current_user):
    """创建新的模型配置"""
    if current_user.role not in ['admin']:
        return jsonify({
            'code': 403,
            'message': '权限不足，只有管理员可以创建模型配置',
            'data': None
        }), 403

    from backend.app.services.model_config_service import ModelConfigService

    try:
        data = request.get_json()

        # 验证必需字段
        required_fields = ['model_id', 'name', 'provider', 'api_key', 'base_url', 'model']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "code": 400,
                    "message": f"缺少必需字段: {field}",
                    "data": None
                }), 400

        model = ModelConfigService.create_model(data)
        return jsonify({
            "code": 201,
            "message": "模型配置创建成功",
            "data": model
        }), 201

    except ValueError as e:
        return jsonify({
            "code": 400,
            "message": str(e),
            "data": None
        }), 400
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"创建模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models/<string:model_id>', methods=['PUT'])
@token_required
def update_model(current_user, model_id):
    """更新模型配置"""
    if current_user.role not in ['admin']:
        return jsonify({
            'code': 403,
            'message': '权限不足，只有管理员可以更新模型配置',
            'data': None
        }), 403

    from backend.app.services.model_config_service import ModelConfigService

    try:
        data = request.get_json()
        model = ModelConfigService.update_model(model_id, data)
        return jsonify({
            "code": 200,
            "message": "模型配置更新成功",
            "data": model
        })

    except ValueError as e:
        return jsonify({
            "code": 404,
            "message": str(e),
            "data": None
        }), 404
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"更新模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models/<string:model_id>', methods=['DELETE'])
@token_required
def delete_model(current_user, model_id):
    """删除模型配置"""
    if current_user.role not in ['admin']:
        return jsonify({
            'code': 403,
            'message': '权限不足，只有管理员可以删除模型配置',
            'data': None
        }), 403

    from backend.app.services.model_config_service import ModelConfigService

    try:
        ModelConfigService.delete_model(model_id)
        return jsonify({
            "code": 200,
            "message": "模型配置删除成功",
            "data": None
        })

    except ValueError as e:
        return jsonify({
            "code": 404,
            "message": str(e),
            "data": None
        }), 404
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"删除模型配置失败: {str(e)}",
            "data": None
        }), 500

@bp.route('/models/<string:model_id>/set-default', methods=['POST'])
@token_required
def set_default_model(current_user, model_id):
    """设置默认模型"""
    if current_user.role not in ['admin']:
        return jsonify({
            'code': 403,
            'message': '权限不足，只有管理员可以设置默认模型',
            'data': None
        }), 403

    from backend.app.services.model_config_service import ModelConfigService

    try:
        model = ModelConfigService.set_default_model(model_id)
        return jsonify({
            "code": 200,
            "message": "默认模型设置成功",
            "data": model
        })

    except ValueError as e:
        return jsonify({
            "code": 404,
            "message": str(e),
            "data": None
        }), 404
    except Exception as e:
        return jsonify({
            "code": 500,
            "message": f"设置默认模型失败: {str(e)}",
            "data": None
        }), 500
