from backend.app.models.models import LLMConfig
from backend.app.services.model_config_service import ModelConfigService
from openai import OpenAI
import json

class LLMService:
    """
    大语言模型服务类
    处理与LLM的交互，包括排班表生成等
    """

    def __init__(self, model_id=None):
        """
        初始化LLM服务
        :param model_id: 模型ID，如果为None则使用默认模型
        """
        if model_id:
            # 使用指定的模型配置
            self.config = ModelConfigService.get_model_by_id(model_id)
        else:
            # 使用默认模型配置
            self.config = ModelConfigService.get_default_model()

        if self.config:
            self.client = OpenAI(
                base_url=self.config['base_url'],
                api_key=self.config['api_key']
            )
            self.model = self.config['model']
            self.max_tokens = self.config.get('max_tokens', 4000)
            self.temperature = self.config.get('temperature', 0.7)
            self.model_name = self.config['name']
        else:
            # 使用硬编码的备用配置
            self.client = OpenAI(
                base_url='https://api.siliconflow.cn/v1',
                api_key='sk-siiswpsbpksaraejiwejzaiejlbfrjdysshlnlhpvpqzfruu'
            )
            self.model = "Qwen/Qwen3-235B-A22B-Instruct-2507"
            self.max_tokens = 4000
            self.temperature = 0.7
            self.model_name = "备用模型"
    
    def generate_schedule(self, requirements, nurses, positions, department, start_date, end_date):
        """
        根据需求生成排班表
        :param requirements: 排班需求描述
        :param nurses: 护士列表
        :param positions: 岗位列表
        :param department: 科室信息
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 生成的排班表
        """
        # 构建提示词
        prompt = self._build_schedule_prompt(requirements, nurses, positions, department, start_date, end_date)
        
        # 调用LLM生成排班表
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": f"你是一个专业的护士排班助手（使用{self.model_name}），请根据提供的信息生成合理的排班表。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            stream=False  # 非流式输出
        )
        
        full_content = completion.choices[0].message.content
        
        # 清理可能的Markdown包装和其他文本
        # 移除可能的 "json" 文本前缀
        full_content = full_content.strip()
        if full_content.startswith("json"):
            full_content = full_content[4:].strip()
            
        # 移除可能的Markdown代码块标记
        if full_content.startswith("```"):
            # 找到第一个换行符的位置
            first_newline = full_content.find('\n')
            if first_newline != -1:
                full_content = full_content[first_newline + 1:]
        
        # 移除结尾的代码块标记
        if full_content.endswith("```"):
            full_content = full_content[:-3]
            
        # 再次清理空白字符
        full_content = full_content.strip()
        
        # 尝试解析为JSON格式
        try:
            return json.loads(full_content)
        except json.JSONDecodeError as e:
            # 如果不是JSON格式，抛出异常
            raise Exception(f"LLM返回的内容不是有效的JSON格式: {full_content}") from e
    
    def _build_schedule_prompt(self, requirements, nurses, positions, department, start_date, end_date):
        """
        构建排班提示词
        """
        prompt = f"""
        请根据以下信息生成一份护士排班表：
        
        排班周期：
        - 开始日期：{start_date}
        - 结束日期：{end_date}
        
        科室信息：
        - 科室名称：{department.name}
        - 科室描述：{department.description or '无'}
        
        排班需求：
        {requirements}
        
        可用护士列表：
        """
        
        for nurse in nurses:
            prompt += f"- {nurse.name} (工号: {nurse.employee_id}"
            if nurse.positions:
                prompt += f", 胜任岗位: {[p.name for p in nurse.positions]}"
            prompt += ")\n"
        
        prompt += "\n岗位列表：\n"
        for position in positions:
            prompt += f"- {position.name}: {position.description or '无描述'}\n"
        
        prompt += f"""
        
        要求：
        1. 生成从{start_date}到{end_date}的排班表
        2. 明确指定每天每班次的护士和岗位
        3. 考虑护士的岗位胜任能力
        4. 确保排班合理性和公平性
        5. 必须严格按照以下标准JSON格式输出结果：
           {{
             "schedule": [
               {{
                 "date": "日期 (格式: YYYY-MM-DD)",
                 "shift": "班次 (如: 早班/中班/夜班)",
                 "nurse_name": "护士姓名",
                 "position": "岗位名称"
               }}
             ]
           }}
        6. 不要输出任何其他内容，只输出JSON
        7. 确保每个日期都有合理的人员安排
        """
        
        return prompt
    
    def analyze_schedule(self, schedule_data):
        """
        分析排班表，提供统计信息和建议
        :param schedule_data: 排班表数据
        :return: 分析结果
        """
        prompt = f"""
        请分析以下排班表数据并提供统计信息和改进建议：
        
        排班表数据：
        {json.dumps(schedule_data, ensure_ascii=False, indent=2)}
        
        要求：
        1. 统计每位护士的工作天数和班次分布
        2. 检查是否有违反排班规则的情况
        3. 提供改进建议
        4. 严格按照以下标准JSON格式输出结果：
           {{
             "statistics": {{
               "total_days": "总天数",
               "nurse_workload": [
                 {{
                   "nurse_name": "护士姓名",
                   "total_shifts": "总班次",
                   "shift_breakdown": {{ "早班": 数量, "中班": 数量, "夜班": 数量 }}
                 }}
               ]
             }},
             "issues": [
               {{
                 "description": "问题描述"
               }}
             ],
             "suggestions": [
               "改进建议"
             ]
           }}
        5. 不要输出任何其他内容，只输出JSON
        """
        
        # 使用 OpenAI 客户端而不是直接的 HTTP 请求
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "你是一个专业的排班分析师，请分析排班表并提供统计信息和改进建议。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=4000,
            stream=False
        )

        try:
            content = completion.choices[0].message.content
            if not content:
                raise Exception("LLM返回的内容为空")

            # 清理可能的Markdown包装
            if content.startswith("```json"):
                content = content[7:]  # 移除开头的 ```json
            if content.endswith("```"):
                content = content[:-3]  # 移除结尾的 ```

            return json.loads(content)
        except (json.JSONDecodeError, KeyError) as e:
            content = ""
            raise Exception(f"LLM返回的内容不是有效的JSON格式: {content}") from e
    
    def generate_schedule_stream(self, requirements, nurses, positions, department, start_date, end_date):
        """
        根据需求生成排班表（流式输出版本）
        :param requirements: 排班需求描述
        :param nurses: 护士列表
        :param positions: 岗位列表
        :param department: 科室信息
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 生成的排班表（流式）
        """
        # 构建提示词
        prompt = self._build_schedule_prompt(requirements, nurses, positions, department, start_date, end_date)
        
        # 调用LLM生成排班表（流式）
        stream = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": f"你是一个专业的护士排班助手（使用{self.model_name}），请根据提供的信息生成合理的排班表。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            stream=True  # 使用流式输出
        )

        return stream