<template>
  <div class="ai-schedule-container">
    <!-- 配置区域 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>🤖 AI智能排班助手</span>
          <el-button-group>
            <el-button
              :type="viewMode === 'chat' ? 'primary' : ''"
              @click="viewMode = 'chat'"
              :icon="ChatDotRound"
            >
              对话模式
            </el-button>
            <el-button
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
              :icon="Grid"
            >
              表格模式
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 基础配置 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        :disabled="generating"
        inline
      >
        <el-form-item label="科室" prop="department_id">
          <el-select v-model="form.department_id" placeholder="请选择科室" style="width: 200px">
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="AI模型" prop="model_id">
          <el-select v-model="form.model_id" placeholder="请选择AI模型" style="width: 200px">
            <el-option
              v-for="model in availableModels"
              :key="model.model_id"
              :label="model.name"
              :value="model.model_id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ model.name }}</span>
                <el-tag v-if="model.is_default" type="success" size="small">默认</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="排班表名称" prop="schedule_name">
          <el-input v-model="form.schedule_name" placeholder="请输入排班表名称" style="width: 200px" />
        </el-form-item>

        <el-form-item label="排班时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 对话模式 -->
    <el-card v-if="viewMode === 'chat'" class="chat-card">
      <template #header>
        <div class="chat-header">
          <span>💬 与AI助手对话</span>
          <el-button @click="clearChat" size="small" type="danger" plain>清空对话</el-button>
        </div>
      </template>

      <!-- 对话历史 -->
      <div class="chat-container" ref="chatContainer">
        <div v-for="(message, index) in chatHistory" :key="index" class="chat-message">
          <div v-if="message.role === 'user'" class="user-message">
            <div class="message-avatar">👤</div>
            <div class="message-content">{{ message.content }}</div>
          </div>
          <div v-else class="ai-message">
            <div class="message-avatar">🤖</div>
            <div class="message-content">
              <div v-if="message.type === 'markdown'" class="markdown-content" v-html="renderMarkdown(message.content)"></div>
              <div v-else>{{ message.content }}</div>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="generating" class="ai-message typing">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <el-input
          v-model="currentMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入您的排班需求，例如：请为西区安排一周排班，每天需要责任护士2名..."
          @keydown.ctrl.enter="sendMessage"
          :disabled="generating"
        />
        <div class="input-actions">
          <el-button @click="sendMessage" type="primary" :loading="generating" :disabled="!currentMessage.trim()">
            发送 (Ctrl+Enter)
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格模式 -->
    <el-card v-if="viewMode === 'table'" class="table-card">
      <template #header>
        <div class="table-header">
          <span>📊 排班表格</span>
          <el-button-group>
            <el-button @click="generateFromForm" type="primary" :loading="generating">
              生成排班表
            </el-button>
            <el-button @click="exportSchedule" :disabled="!scheduleData">
              导出表格
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 快速配置 -->
      <div class="quick-config">
        <el-form inline>
          <el-form-item label="排班要求">
            <el-input
              v-model="form.requirements"
              type="textarea"
              :rows="2"
              placeholder="请输入排班要求..."
              style="width: 400px"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="generateFromForm" type="primary" :loading="generating">
              生成
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 排班表格显示 -->
      <div v-if="scheduleTableData.length > 0" class="schedule-table-container">
        <el-table :data="scheduleTableData" border stripe style="width: 100%">
          <el-table-column prop="nurse" label="护士姓名" width="120" fixed="left" />
          <el-table-column
            v-for="day in tableWeekDays"
            :key="day.key"
            :prop="day.key"
            :label="day.label"
            width="140"
          >
            <template #default="scope">
              <div class="schedule-cell" :class="getPositionClass(scope.row[day.key])">
                {{ scope.row[day.key] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="暂无排班数据，请先生成排班表" />
    </el-card>

    <!-- 实时生成区域 -->
    <el-card v-if="showGenerationArea" class="generation-area">
      <template #header>
        <div class="generation-header">
          <span>{{ form.schedule_name || '排班表' }}</span>
          <el-tag v-if="generating" type="primary" effect="plain">
            <el-icon class="is-loading"><Loading /></el-icon>
            正在生成中...
          </el-tag>
          <el-tag v-else type="success" effect="plain">
            <el-icon><Check /></el-icon>
            生成完成
          </el-tag>
        </div>
      </template>

      <!-- 实时Markdown表格显示 -->
      <div class="schedule-display-container">
        <!-- 实时排班表格 -->
        <div class="schedule-table-container">
          <h4>AI智能排班表 - 实时生成中...</h4>
          <div class="custom-table">
            <!-- 表头 -->
            <div class="table-header">
              <div class="header-cell nurse-name-header">护士姓名</div>
              <div
                v-for="day in weekDays"
                :key="day.date"
                class="header-cell date-header"
              >
                {{ day.label }}
              </div>
            </div>

            <!-- 表格内容 -->
            <div v-if="nurseList.length === 0" class="empty-table">
              <div class="empty-message">
                <el-icon class="loading-icon"><Loading /></el-icon>
                等待AI生成排班数据...
              </div>
            </div>

            <div v-else class="table-body">
              <div
                v-for="nurseName in nurseList"
                :key="nurseName"
                class="table-row"
                :class="{ 'row-new': isNewNurse(nurseName) }"
              >
                <div class="body-cell nurse-name-cell">
                  {{ nurseName }}
                  <span v-if="isNewNurse(nurseName)" class="new-badge">新增</span>
                </div>
                <div
                  v-for="day in weekDays"
                  :key="day.date"
                  class="body-cell schedule-cell"
                >
                  <div
                    v-if="scheduleData[nurseName] && scheduleData[nurseName][day.date]"
                    class="schedule-content"
                    :class="{
                      'schedule-content-new': scheduleData[nurseName][day.date]?.isNew,
                      'schedule-content-appear': scheduleData[nurseName][day.date]?.isAppearing,
                      [getPositionClass(scheduleData[nurseName][day.date].position)]: true
                    }"
                  >
                    <div class="nurse-position-info">
                      <div class="nurse-name-display">{{ nurseName }}</div>
                      <div class="position-short">
                        ({{ getPositionInfo(scheduleData[nurseName][day.date].position)?.short_name || scheduleData[nurseName][day.date].position }})
                      </div>
                    </div>
                  </div>
                  <div v-else class="empty-cell">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI生成过程输出 -->
      <el-collapse v-if="streamOutput" class="generation-log">
        <el-collapse-item title="查看AI生成过程" name="log">
          <div class="stream-output">
            <pre>{{ streamOutput }}</pre>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 新护士和岗位确认对话框 -->
    <el-dialog
      v-model="showConfirmDialog"
      title="发现新的护士和岗位"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="analysisResult">
        <div v-if="analysisResult.new_nurses && analysisResult.new_nurses.length > 0" class="mb-4">
          <h4 class="text-lg font-semibold mb-2">
            <el-icon class="mr-1"><User /></el-icon>
            新护士 ({{ analysisResult.new_nurses.length }}人)
          </h4>
          <div class="bg-blue-50 p-3 rounded">
            <el-tag
              v-for="nurse in analysisResult.new_nurses"
              :key="nurse"
              type="primary"
              class="mr-2 mb-2"
            >
              {{ nurse }}
            </el-tag>
          </div>
          <p class="text-sm text-gray-600 mt-2">
            这些护士不在系统名单中，是否添加到系统？
          </p>
        </div>

        <div v-if="analysisResult.new_positions && analysisResult.new_positions.length > 0" class="mb-4">
          <h4 class="text-lg font-semibold mb-2">
            <el-icon class="mr-1"><Briefcase /></el-icon>
            新岗位 ({{ analysisResult.new_positions.length }}个)
          </h4>
          <div class="bg-green-50 p-3 rounded">
            <el-tag
              v-for="position in analysisResult.new_positions"
              :key="position"
              type="success"
              class="mr-2 mb-2"
            >
              {{ position }}
            </el-tag>
          </div>
          <p class="text-sm text-gray-600 mt-2">
            这些岗位不在系统中，是否添加到系统？
          </p>
        </div>

        <div v-if="(!analysisResult.new_nurses || analysisResult.new_nurses.length === 0) &&
                   (!analysisResult.new_positions || analysisResult.new_positions.length === 0)"
             class="text-center py-4">
          <el-icon class="text-4xl text-green-500 mb-2"><Check /></el-icon>
          <p class="text-lg text-green-600">所有护士和岗位都已在系统中</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showConfirmDialog = false">取消</el-button>
          <el-button
            v-if="(analysisResult?.new_nurses?.length > 0) || (analysisResult?.new_positions?.length > 0)"
            type="primary"
            @click="confirmAddMissingData"
            :loading="isAddingData"
          >
            确认添加
          </el-button>
          <el-button
            v-else
            type="primary"
            @click="showConfirmDialog = false"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Grid, MagicStick, Loading, Check, User, Briefcase } from '@element-plus/icons-vue'
import { departmentAPI, positionAPI, modelAPI } from '../services/api'
import { marked } from 'marked'

// 响应式数据
const formRef = ref()
const generating = ref(false)
const showGenerationArea = ref(false)
const streamOutput = ref('')
const departments = ref<any[]>([])
const positions = ref<any[]>([])
const availableModels = ref<any[]>([])
const analysisResult = ref<any>(null)
const showConfirmDialog = ref(false)
const isAddingData = ref(false)

// 新增：对话模式相关数据
const viewMode = ref<'chat' | 'table'>('chat')
const chatHistory = ref<Array<{role: 'user' | 'ai', content: string, type?: 'text' | 'markdown'}>>([])
const currentMessage = ref('')
const chatContainer = ref<HTMLElement>()

// 新增：表格模式相关数据
const scheduleTableData = ref<any[]>([])
const tableWeekDays = ref([
  { key: 'monday', label: '周一' },
  { key: 'tuesday', label: '周二' },
  { key: 'wednesday', label: '周三' },
  { key: 'thursday', label: '周四' },
  { key: 'friday', label: '周五' },
  { key: 'saturday', label: '周六' },
  { key: 'sunday', label: '周日' }
])
// 表单数据
const form = reactive({
  department_id: 1, // 默认东区
  model_id: '', // AI模型ID
  schedule_name: '2025年第31周排班表',
  requirements: '每天需要责任护士1名，值班护士1名。白班时间8:00-16:00，夜班时间16:00-次日8:00。确保每个班次都有足够的护理人员，优先安排有经验的护士担任责任护士。'
})

// 日期范围
const dateRange = ref<string[]>([])

// 排班数据
const scheduleData = ref<Record<string, Record<string, any>>>({})
const nurseList = ref<string[]>([])
const weekDays = ref<Array<{date: string, label: string}>>([])

// 实时显示相关
const newNurses = ref<Set<string>>(new Set()) // 新增的护士
const recentlyAddedItems = ref<Set<string>>(new Set()) // 最近添加的排班项目

// 表单验证规则
const rules = {
  department_id: [{ required: true, message: '请选择科室', trigger: 'change' }],
  schedule_name: [{ required: true, message: '请输入排班表名称', trigger: 'blur' }],
  requirements: [{ required: true, message: '请输入排班要求', trigger: 'blur' }]
}

// 获取默认日期范围（本周）
const getDefaultDateRange = () => {
  const today = new Date()
  const monday = new Date(today)
  monday.setDate(today.getDate() - today.getDay() + 1)
  
  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6)
  
  return [
    monday.toISOString().split('T')[0],
    sunday.toISOString().split('T')[0]
  ]
}

// 初始化周日期
const initWeekDays = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    console.log('❌ 日期范围未设置:', dateRange.value)
    return
  }

  const startDate = new Date(dateRange.value[0])
  const endDate = new Date(dateRange.value[1])

  console.log('📅 初始化日期范围:', dateRange.value[0], '到', dateRange.value[1])

  const days: Array<{date: string, label: string}> = []
  const current = new Date(startDate)

  while (current <= endDate) {
    const dateStr = current.toISOString().split('T')[0]
    const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][current.getDay()]
    const monthDay = `${current.getMonth() + 1}/${current.getDate()}`

    days.push({
      date: dateStr,
      label: `${monthDay} ${weekDay}`
    })

    current.setDate(current.getDate() + 1)
  }

  weekDays.value = days
  console.log('📅 初始化的周日期:', weekDays.value)
}

// 生成排班表
const generateSchedule = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      generating.value = true
      showGenerationArea.value = true
      streamOutput.value = ''
      scheduleData.value = {}
      nurseList.value = []

      // 重置实时显示状态
      newNurses.value.clear()
      recentlyAddedItems.value.clear()
      
      // 初始化日期
      initWeekDays()
      
      try {
        await generateWithStream()
      } catch (error: any) {
        console.error('生成失败:', error)
        ElMessage.error(error.message || '生成排班表失败')
      } finally {
        generating.value = false
      }
    }
  })
}

// 检查并获取有效token
const getValidToken = async () => {
  let token = localStorage.getItem('token')
  console.log('🔐 检查token:', token ? `${token.substring(0, 20)}...` : 'null')

  if (!token) {
    ElMessage.error('未找到认证token，请重新登录')
    // 跳转到登录页面
    window.location.href = '/login'
    return null
  }

  // 测试token有效性
  try {
    const testResponse = await fetch('/api/v1/departments', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    console.log('🔍 Token验证响应:', testResponse.status)

    if (testResponse.status === 401) {
      console.log('❌ Token已过期')
      ElMessage.error('登录已过期，请重新登录')
      localStorage.removeItem('token')
      window.location.href = '/login'
      return null
    }

    if (!testResponse.ok) {
      console.log('⚠️ Token验证失败:', testResponse.status)
      ElMessage.error('Token验证失败，请重新登录')
      return null
    }

    console.log('✅ Token有效')
    return token

  } catch (error) {
    console.error('❌ Token验证异常:', error)
    ElMessage.error('网络错误，请检查连接')
    return null
  }
}

// 处理API错误响应
const handleApiError = async (response: Response) => {
  if (response.status === 401) {
    ElMessage.error('登录已过期，请重新登录')
    localStorage.removeItem('token')
    // 跳转到登录页面
    setTimeout(() => {
      window.location.href = '/login'
    }, 1500)
    return true
  }
  return false
}

// 流式生成
const generateWithStream = async () => {
  const token = await getValidToken()
  if (!token) return

  const requestData = {
    department_id: form.department_id,
    requirements: form.requirements,
    schedule_name: form.schedule_name,
    start_date: dateRange.value[0],
    end_date: dateRange.value[1],
    model_id: form.model_id  // 添加模型ID
  }

  console.log('📝 发送排班请求:', requestData)

  const response = await fetch('/api/v1/schedules/generate-stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(requestData)
  })

  if (!response.ok) {
    console.log('❌ 排班请求失败:', response.status, response.statusText)

    let errorMessage = `请求失败: ${response.status}`
    try {
      const errorText = await response.text()
      console.log('❌ 错误详情:', errorText)

      // 尝试解析JSON错误信息
      try {
        const errorData = JSON.parse(errorText)
        errorMessage = errorData.message || errorMessage
      } catch (e) {
        errorMessage = errorText || errorMessage
      }
    } catch (e) {
      console.log('⚠️ 无法读取错误响应')
    }

    if (response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      localStorage.removeItem('token')
      setTimeout(() => {
        window.location.href = '/login'
      }, 1500)
      return
    }

    throw new Error(errorMessage)
  }

  const reader = response.body?.getReader()
  if (!reader) {
    throw new Error('无法读取响应流')
  }

  const decoder = new TextDecoder()
  let fullContent = ''

  while (true) {
    const { done, value } = await reader.read()
    if (done) break

    const chunk = decoder.decode(value, { stream: true })
    const lines = chunk.split('\n')
    
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6)
        if (data === '[DONE]') {
          // 流式响应结束，但不再重复解析，因为实时解析已经处理了所有数据
          console.log('🏁 流式响应结束')
          return
        }

        try {
          const parsed = JSON.parse(data)

          if (parsed.type === 'completion') {
            // 处理完成消息，包含分析结果
            console.log('🎉 AI排班生成完成!')
            ElMessage.success(parsed.message)

            if (parsed.analysis) {
              analysisResult.value = parsed.analysis
              console.log('📊 收到分析结果:', parsed.analysis)

              // 如果有新的护士或岗位，显示确认对话框
              if ((parsed.analysis.new_nurses && parsed.analysis.new_nurses.length > 0) ||
                  (parsed.analysis.new_positions && parsed.analysis.new_positions.length > 0)) {
                console.log('🔍 发现新数据，显示确认对话框')
                showConfirmDialog.value = true
              } else {
                console.log('✅ 所有护士和岗位都已在系统中')
              }
            }

            // 显示最终统计
            console.log('📋 最终排班数据:', scheduleData.value)
            console.log('👥 最终护士列表:', nurseList.value)

            return // 完成处理，不继续处理其他逻辑
          } else if (parsed.type === 'success' || parsed.type === 'error' || parsed.type === 'info') {
            ElMessage[parsed.type === 'success' ? 'success' : parsed.type === 'error' ? 'error' : 'info'](parsed.message)
          } else {
            const content = parsed.choices?.[0]?.delta?.content || ''
            if (content) {
              fullContent += content
              streamOutput.value += content
              processStreamContent(content)
            }
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
  }
}

// 处理流式内容
const processStreamContent = (content: string) => {
  console.log('收到流式内容:', content)
  // 实时增量解析逻辑
  processIncrementalJSON(content)
}

// 增量JSON解析 - 真正的实时逐项解析
let jsonBuffer = ''
let inJsonBlock = false
let inScheduleArray = false
let currentItem = ''
let braceDepth = 0
let inString = false
let escapeNext = false

const processIncrementalJSON = (content: string) => {
  for (let i = 0; i < content.length; i++) {
    const char = content[i]
    jsonBuffer += char

    // 检测JSON块开始
    if (!inJsonBlock && jsonBuffer.includes('```json')) {
      console.log('🟢 检测到JSON块开始')
      inJsonBlock = true
      jsonBuffer = jsonBuffer.substring(jsonBuffer.indexOf('```json') + 7)
      // 重置状态
      inScheduleArray = false
      currentItem = ''
      braceDepth = 0
      inString = false
      escapeNext = false
      continue
    }

    if (inJsonBlock) {
      // 处理转义字符
      if (escapeNext) {
        currentItem += char
        escapeNext = false
        continue
      }

      if (char === '\\') {
        escapeNext = true
        currentItem += char
        continue
      }

      // 处理字符串状态
      if (char === '"' && !escapeNext) {
        inString = !inString
      }

      // 检测schedule数组开始
      if (!inScheduleArray && !inString) {
        const recentBuffer = jsonBuffer.slice(-20) // 检查最近20个字符
        if (recentBuffer.includes('"schedule"') && char === '[') {
          console.log('🟡 检测到schedule数组开始')
          inScheduleArray = true
          currentItem = ''
          braceDepth = 0
          continue
        }
      }

      // 在schedule数组内处理
      if (inScheduleArray && !inString) {
        if (char === '{') {
          braceDepth++
          if (braceDepth === 1) {
            currentItem = char // 开始新的项目
            continue
          }
        } else if (char === '}') {
          currentItem += char
          braceDepth--

          if (braceDepth === 0) {
            // 完成一个排班项目，立即解析并显示
            console.log('🔥 完成一个排班项目:', currentItem)
            tryParseAndDisplayItem(currentItem)
            currentItem = ''
            continue
          }
        } else if (char === ']') {
          // schedule数组结束
          inScheduleArray = false
          continue
        }
      }

      // 累积当前项目内容
      if (inScheduleArray && braceDepth > 0) {
        currentItem += char
      }

      // 检测JSON块结束
      if (jsonBuffer.includes('```')) {
        inJsonBlock = false
        break
      }
    }
  }
}

// 尝试解析并显示单个排班项目
const tryParseAndDisplayItem = (itemStr: string) => {
  try {
    // 清理项目字符串
    let cleanedItem = itemStr.trim()

    // 确保是完整的JSON对象
    if (!cleanedItem.startsWith('{')) {
      console.log('⚠️ 排班项目不是完整的JSON对象，跳过')
      return
    }

    const parsed = JSON.parse(cleanedItem)
    if (parsed.nurse_name && parsed.date && parsed.position && parsed.shift) {
      console.log('✅ 实时解析到排班项目:', parsed)
      updateScheduleTable(parsed.nurse_name, parsed.date, parsed.position, parsed.shift)
    } else {
      console.log('⚠️ 排班项目缺少必要字段:', parsed)
    }
  } catch (e) {
    console.log('⚠️ 解析排班项目失败，继续处理:', e.message)
    // 忽略解析错误，继续处理
  }
}

// 更新排班表 - 直接在表格中实时显示
const updateScheduleTable = (nurseName: string, date: string, position: string, shift: string) => {
  console.log(`🔥 实时填充: ${nurseName} - ${date} - ${position} - ${shift}`)

  // 确保护士在列表中
  if (!nurseList.value.includes(nurseName)) {
    nurseList.value.push(nurseName)
    newNurses.value.add(nurseName) // 标记为新增护士
    console.log(`✅ 新增护士: ${nurseName}`)

    // 3秒后移除新增标记
    setTimeout(() => {
      newNurses.value.delete(nurseName)
    }, 3000)
  }

  // 确保护士有排班数据对象
  if (!scheduleData.value[nurseName]) {
    scheduleData.value[nurseName] = {}
  }

  // 添加新的排班项目到数据，带有动画标记
  scheduleData.value[nurseName][date] = {
    position,
    shift,
    isNew: true,
    isAppearing: true
  }

  // 创建唯一标识符用于跟踪最近添加的项目
  const itemKey = `${nurseName}-${date}`
  recentlyAddedItems.value.add(itemKey)

  // 动画效果：先显示出现动画，然后显示新增高亮
  setTimeout(() => {
    if (scheduleData.value[nurseName] && scheduleData.value[nurseName][date]) {
      scheduleData.value[nurseName][date].isAppearing = false
    }
  }, 500)

  // 3秒后移除新增高亮
  setTimeout(() => {
    if (scheduleData.value[nurseName] && scheduleData.value[nurseName][date]) {
      scheduleData.value[nurseName][date].isNew = false
    }
    recentlyAddedItems.value.delete(itemKey)
  }, 3000)

  console.log('✅ 当前排班数据:', scheduleData.value)
  console.log('✅ 当前护士列表:', nurseList.value)
}

// 检查是否为新增护士
const isNewNurse = (nurseName: string) => {
  return newNurses.value.has(nurseName)
}

// 根据岗位名称获取岗位信息
const getPositionInfo = (positionName: string) => {
  return positions.value.find(pos => pos.name === positionName || pos.short_name === positionName)
}

// 根据岗位获取CSS类名
const getPositionClass = (positionName: string) => {
  const positionInfo = getPositionInfo(positionName)
  if (!positionInfo) return 'position-default'

  // 根据岗位类型返回不同的CSS类
  if (positionInfo.short_name?.includes('责')) return 'position-responsibility'
  if (positionInfo.short_name?.includes('值')) return 'position-duty'
  if (positionInfo.short_name?.includes('治')) return 'position-treatment'
  if (positionInfo.short_name?.includes('夜')) return 'position-night'
  if (positionInfo.short_name?.includes('听')) return 'position-standby'
  if (positionInfo.short_name?.includes('休')) return 'position-rest'
  if (positionInfo.short_name?.includes('管')) return 'position-management'

  return 'position-default'
}

// 确认添加缺失的数据
const confirmAddMissingData = async () => {
  if (!analysisResult.value) return

  isAddingData.value = true

  try {
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('未找到认证token，请重新登录')
      return
    }

    const requestData = {
      new_nurses: analysisResult.value.new_nurses || [],
      new_positions: analysisResult.value.new_positions || [],
      department_id: form.department_id
    }

    const response = await fetch('/api/v1/schedules/add-missing-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestData)
    })

    const result = await response.json()

    if (response.ok) {
      ElMessage.success(`成功添加 ${result.data.nurses_count} 个护士和 ${result.data.positions_count} 个岗位`)

      // 重新加载护士和岗位数据
      await loadNursesAndPositions()

      showConfirmDialog.value = false
      analysisResult.value = null
    } else {
      ElMessage.error(result.message || '添加数据失败')
    }
  } catch (error: any) {
    console.error('添加数据失败:', error)
    ElMessage.error(error.message || '添加数据失败')
  } finally {
    isAddingData.value = false
  }
}

// 重新加载护士和岗位数据
const loadNursesAndPositions = async () => {
  try {
    // 重新加载岗位数据
    const posResponse = await positionAPI.getAll()
    positions.value = posResponse.data?.data || []

    // 可以在这里添加重新加载护士数据的逻辑
    console.log('数据重新加载完成')
  } catch (error) {
    console.error('重新加载数据失败:', error)
  }
}

// 解析完整内容
const parseCompleteContent = (content: string) => {
  try {
    console.log('🔍 开始解析完整内容，长度:', content.length)

    let cleaned = content.trim()

    // 移除markdown代码块标记
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.substring(7)
    }
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.substring(0, cleaned.length - 3)
    }

    // 再次清理
    cleaned = cleaned.trim()

    console.log('🧹 清理后内容长度:', cleaned.length)
    console.log('🧹 清理后内容前200字符:', cleaned.substring(0, 200))

    // 尝试修复不完整的JSON
    cleaned = fixIncompleteJSON(cleaned)

    console.log('🔧 修复后准备解析JSON...')
    const data = JSON.parse(cleaned)

    if (data.schedule && Array.isArray(data.schedule)) {
      console.log('✅ 成功解析到排班数据，条数:', data.schedule.length)

      data.schedule.forEach((item: any) => {
        if (item.nurse_name && item.date && item.position && item.shift) {
          updateScheduleTable(item.nurse_name, item.date, item.position, item.shift)
        }
      })
    } else {
      console.warn('⚠️ 解析的数据中没有有效的schedule数组')
    }

    console.log('🎉 排班表生成完成')
  } catch (e) {
    console.error('❌ 解析完整内容失败:', e)
    console.log('📝 失败的内容:', content.substring(0, 500))

    // 尝试从已有的实时解析数据中恢复
    console.log('🔄 尝试使用已解析的实时数据...')
    if (Object.keys(scheduleData.value).length > 0) {
      console.log('✅ 使用实时解析的数据作为最终结果')
    }
  }
}

// 修复不完整的JSON
const fixIncompleteJSON = (jsonStr: string): string => {
  try {
    // 如果JSON已经完整，直接返回
    JSON.parse(jsonStr)
    return jsonStr
  } catch (e) {
    console.log('🔧 JSON不完整，尝试修复...')

    let fixed = jsonStr.trim()

    // 确保以{开始
    if (!fixed.startsWith('{')) {
      const startIndex = fixed.indexOf('{')
      if (startIndex > 0) {
        fixed = fixed.substring(startIndex)
      }
    }

    // 尝试补全缺失的结束符
    let openBraces = 0
    let openBrackets = 0
    let inString = false
    let escapeNext = false

    for (let i = 0; i < fixed.length; i++) {
      const char = fixed[i]

      if (escapeNext) {
        escapeNext = false
        continue
      }

      if (char === '\\') {
        escapeNext = true
        continue
      }

      if (char === '"' && !escapeNext) {
        inString = !inString
        continue
      }

      if (!inString) {
        if (char === '{') openBraces++
        else if (char === '}') openBraces--
        else if (char === '[') openBrackets++
        else if (char === ']') openBrackets--
      }
    }

    // 补全缺失的结束符
    while (openBrackets > 0) {
      fixed += ']'
      openBrackets--
    }

    while (openBraces > 0) {
      fixed += '}'
      openBraces--
    }

    console.log('🔧 修复后的JSON长度:', fixed.length)

    // 验证修复结果
    try {
      JSON.parse(fixed)
      console.log('✅ JSON修复成功')
      return fixed
    } catch (e2) {
      console.log('❌ JSON修复失败，返回原始内容')
      return jsonStr
    }
  }
}

// 新增：对话模式相关方法
const renderMarkdown = (content: string) => {
  try {
    return marked(content)
  } catch (error) {
    return content
  }
}

const clearChat = () => {
  chatHistory.value = []
  currentMessage.value = ''
}

const sendMessage = async () => {
  if (!currentMessage.value.trim() || generating.value) return

  // 添加用户消息
  chatHistory.value.push({
    role: 'user',
    content: currentMessage.value,
    type: 'text'
  })

  const userMessage = currentMessage.value
  currentMessage.value = ''

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始生成
  generating.value = true

  try {
    const requestData = {
      department_id: form.department_id,
      requirements: userMessage,
      schedule_name: form.schedule_name,
      start_date: dateRange.value[0],
      end_date: dateRange.value[1],
      model_id: form.model_id,
      chat_mode: true // 标识为对话模式
    }

    const response = await fetch('/api/v1/schedules/generate-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法读取响应流')
    }

    let aiMessage = ''
    let currentAiMessageIndex = -1

    // 添加AI消息占位符
    chatHistory.value.push({
      role: 'ai',
      content: '',
      type: 'markdown'
    })
    currentAiMessageIndex = chatHistory.value.length - 1

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = new TextDecoder().decode(value)
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6)
          if (data === '[DONE]') {
            break
          }

          try {
            const parsed = JSON.parse(data)
            if (parsed.choices && parsed.choices[0]?.delta?.content) {
              const content = parsed.choices[0].delta.content
              aiMessage += content

              // 更新AI消息
              if (currentAiMessageIndex >= 0) {
                chatHistory.value[currentAiMessageIndex].content = aiMessage
              }

              // 滚动到底部
              await nextTick()
              scrollToBottom()
            }
          } catch (e) {
            // 忽略JSON解析错误
          }
        }
      }
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')

    // 添加错误消息
    chatHistory.value.push({
      role: 'ai',
      content: '抱歉，处理您的请求时出现了错误，请稍后重试。',
      type: 'text'
    })
  } finally {
    generating.value = false
    await nextTick()
    scrollToBottom()
  }
}

const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 新增：表格模式相关方法
const generateFromForm = async () => {
  if (!form.requirements.trim()) {
    ElMessage.warning('请输入排班要求')
    return
  }

  generating.value = true

  try {
    const requestData = {
      department_id: form.department_id,
      requirements: form.requirements,
      schedule_name: form.schedule_name,
      start_date: dateRange.value[0],
      end_date: dateRange.value[1],
      model_id: form.model_id,
      table_mode: true // 标识为表格模式
    }

    const response = await fetch('/api/v1/schedules/generate-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 处理流式响应并解析为表格数据
    await processTableResponse(response)

  } catch (error) {
    console.error('生成排班表失败:', error)
    ElMessage.error('生成排班表失败')
  } finally {
    generating.value = false
  }
}

const processTableResponse = async (response: Response) => {
  const reader = response.body?.getReader()
  if (!reader) return

  let fullContent = ''

  while (true) {
    const { done, value } = await reader.read()
    if (done) break

    const chunk = new TextDecoder().decode(value)
    const lines = chunk.split('\n')

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6)
        if (data === '[DONE]') {
          break
        }

        try {
          const parsed = JSON.parse(data)
          if (parsed.choices && parsed.choices[0]?.delta?.content) {
            fullContent += parsed.choices[0].delta.content
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }
    }
  }

  // 解析生成的内容为表格数据
  parseContentToTable(fullContent)
}

const parseContentToTable = (content: string) => {
  // 这里需要解析AI生成的内容并转换为表格数据
  // 简化实现，实际应该根据AI输出格式进行解析
  try {
    // 尝试从内容中提取JSON格式的排班数据
    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
    if (jsonMatch) {
      const scheduleJson = JSON.parse(jsonMatch[1])
      convertToTableData(scheduleJson)
    } else {
      // 如果没有JSON，尝试解析Markdown表格
      parseMarkdownTable(content)
    }
  } catch (error) {
    console.error('解析排班数据失败:', error)
    ElMessage.error('解析排班数据失败')
  }
}

const convertToTableData = (scheduleJson: any) => {
  const tableData: any[] = []

  if (scheduleJson.schedule) {
    Object.keys(scheduleJson.schedule).forEach(nurseName => {
      const nurseSchedule = scheduleJson.schedule[nurseName]
      const row: any = { nurse: nurseName }

      tableWeekDays.value.forEach(day => {
        const dateKey = Object.keys(nurseSchedule).find(date => {
          const dayOfWeek = new Date(date).getDay()
          const dayIndex = tableWeekDays.value.findIndex(d => d.key === day.key)
          return dayOfWeek === (dayIndex + 1) % 7
        })

        if (dateKey && nurseSchedule[dateKey]) {
          row[day.key] = nurseSchedule[dateKey].position || nurseSchedule[dateKey]
        } else {
          row[day.key] = '-'
        }
      })

      tableData.push(row)
    })
  }

  scheduleTableData.value = tableData
}

const parseMarkdownTable = (content: string) => {
  // 简化的Markdown表格解析
  const lines = content.split('\n')
  const tableData: any[] = []
  let headerFound = false

  for (const line of lines) {
    if (line.includes('|') && line.trim()) {
      const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell)

      if (!headerFound && cells.length > 1) {
        headerFound = true
        continue // 跳过表头
      }

      if (headerFound && cells.length > 1) {
        const row: any = { nurse: cells[0] }
        tableWeekDays.value.forEach((day, index) => {
          row[day.key] = cells[index + 1] || '-'
        })
        tableData.push(row)
      }
    }
  }

  scheduleTableData.value = tableData
}



const exportSchedule = () => {
  if (!scheduleTableData.value.length) {
    ElMessage.warning('暂无排班数据可导出')
    return
  }

  // 简化的导出功能
  const csvContent = generateCSV()
  downloadCSV(csvContent, `${form.schedule_name}.csv`)
}

const generateCSV = () => {
  const headers = ['护士姓名', ...tableWeekDays.value.map(day => day.label)]
  const rows = scheduleTableData.value.map(row => [
    row.nurse,
    ...tableWeekDays.value.map(day => row[day.key])
  ])

  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

// 初始化
onMounted(async () => {
  try {
    // 加载部门数据
    const deptResponse = await departmentAPI.getAll()
    departments.value = deptResponse.data?.data || []

    // 加载岗位数据
    const posResponse = await positionAPI.getAll()
    positions.value = posResponse.data?.data || []

    // 加载可用模型
    const modelResponse = await modelAPI.getEnabled()
    availableModels.value = modelResponse.data?.data || []

    // 设置默认模型
    const defaultModel = availableModels.value.find(m => m.is_default)
    if (defaultModel) {
      form.model_id = defaultModel.model_id
    } else if (availableModels.value.length > 0) {
      form.model_id = availableModels.value[0].model_id
    }

    // 设置默认日期
    dateRange.value = getDefaultDateRange()
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
})
</script>

<style scoped>
.ai-schedule-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.generation-area {
  margin-top: 20px;
}

.generation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.schedule-table-container {
  margin-bottom: 20px;
}

.schedule-table-container h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 18px;
  font-weight: bold;
}

/* 表格实时显示样式 */
.empty-table {
  padding: 40px 0;
  text-align: center;
}

.empty-message {
  color: #6c757d;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 新增护士行动画 */
.row-new {
  animation: rowAppear 1s ease-in-out;
  background: linear-gradient(90deg, #e8f5e8, #f8f9fa) !important;
  border-left: 4px solid #28a745 !important;
}

@keyframes rowAppear {
  0% {
    opacity: 0;
    transform: translateY(-10px);
    background: #e8f5e8;
  }
  50% {
    opacity: 0.8;
    transform: translateY(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 新增护士标记 */
.new-badge {
  display: inline-block;
  background: #28a745;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 新增排班内容动画 */
.schedule-content-appear {
  animation: contentAppear 0.8s ease-in-out;
}

@keyframes contentAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
    background: #fff3cd;
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
    background: #ffeaa7;
  }
  100% {
    opacity: 1;
    transform: scale(1);
    background: transparent;
  }
}

/* 新增排班内容高亮 */
.schedule-content-new {
  background: linear-gradient(45deg, #fff3cd, #ffeaa7) !important;
  border-radius: 4px;
  padding: 4px 8px;
  margin: -4px -8px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
  animation: contentHighlight 3s ease-in-out;
}

@keyframes contentHighlight {
  0% {
    background: #fff3cd;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5);
  }
  50% {
    background: #ffeaa7;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
  }
  100% {
    background: transparent;
    box-shadow: none;
  }
}

/* 最终表格容器样式 */
.final-table-container {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #28a745;
}

.final-table-container h4 {
  margin: 0 0 15px 0;
  color: #28a745;
  font-size: 18px;
  font-weight: bold;
}

/* 实时项目列表样式 */
.real-time-items {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.real-time-items h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
}

.realtime-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.realtime-item:last-child {
  margin-bottom: 0;
}

.realtime-item .nurse-name {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
  margin-right: 15px;
}

.realtime-item .date {
  color: #6c757d;
  font-size: 12px;
  min-width: 80px;
  margin-right: 15px;
}

.realtime-item .position {
  color: #007bff;
  font-weight: 500;
  min-width: 60px;
  margin-right: 15px;
}

.realtime-item .shift {
  color: #28a745;
  font-size: 12px;
  padding: 2px 8px;
  background: #d4edda;
  border-radius: 12px;
}

.item-new {
  animation: realtimeItemAppear 1s ease-in-out;
  background: linear-gradient(45deg, #fff3cd, #ffeaa7) !important;
  border-color: #ffc107 !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

@keyframes realtimeItemAppear {
  0% {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateX(5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 自定义表格样式 */
.custom-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.header-cell {
  padding: 12px 8px;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
}

.nurse-name-header {
  width: 100px;
  min-width: 100px;
  background: #f0f2f5;
  font-size: 13px;
  padding: 8px 6px;
}

.date-header {
  width: 140px;
  min-width: 140px;
}

.header-cell:last-child {
  border-right: none;
}

.empty-table {
  padding: 40px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.body-cell {
  padding: 6px 4px;
  border-right: 1px solid #ebeef5;
  text-align: center;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nurse-name-cell {
  width: 100px;
  min-width: 100px;
  background: #fafafa;
  font-weight: 600;
  color: #303133;
  font-size: 13px;
  padding: 6px 4px;
}

.schedule-cell {
  width: 110px;
  min-width: 110px;
  padding: 4px 2px;
  font-size: 12px;
}

.body-cell:last-child {
  border-right: none;
}

.schedule-content {
  padding: 4px 2px;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s ease;
  width: 100%;
  min-height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.position-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 2px;
}

.position-icon {
  font-size: 14px;
}

.position-text {
  font-weight: 600;
  font-size: 14px;
}

.schedule-content .position {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.schedule-content .shift {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 新的护士姓名和岗位显示样式 */
.nurse-position-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.nurse-name-display {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.2;
}

.position-short {
  font-size: 10px;
  font-weight: 500;
  opacity: 0.8;
  line-height: 1;
}

/* 不同岗位的背景颜色 */
.position-responsibility {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-left: 3px solid #2196f3;
}

.position-duty {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 3px solid #f44336;
}

.position-treatment {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border-left: 3px solid #9c27b0;
}

.position-night {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-left: 3px solid #4caf50;
}

.position-standby {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-left: 3px solid #ff9800;
}

.position-rest {
  background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
  border-left: 3px solid #009688;
}

.position-management {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  border-left: 3px solid #e91e63;
}

.position-default {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
  border-left: 3px solid #9e9e9e;
}

.schedule-content-new {
  animation: scheduleItemAppear 2s ease-in-out;
  background: linear-gradient(45deg, #e6f7ff, #bae7ff) !important;
  border: 2px solid #1890ff !important;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.schedule-content-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: shimmer 1s ease-in-out;
}

@keyframes scheduleItemAppear {
  0% {
    opacity: 0;
    transform: scale(0.7) rotateY(90deg);
    background: #fff2e8;
    box-shadow: 0 0 0 rgba(24, 144, 255, 0);
  }
  25% {
    opacity: 0.6;
    transform: scale(1.15) rotateY(45deg);
    background: #ffe7ba;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.5);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05) rotateY(0deg);
    background: #d4edda;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
  }
  75% {
    opacity: 1;
    transform: scale(1.02);
    background: linear-gradient(45deg, #e6f7ff, #bae7ff);
    box-shadow: 0 6px 15px rgba(24, 144, 255, 0.4);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    background: linear-gradient(45deg, #e6f7ff, #bae7ff);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.empty-cell {
  color: #c0c4cc;
  text-align: center;
  padding: 8px;
}

.generation-log {
  margin-top: 20px;
}

.stream-output {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 新增：对话模式样式 */
.config-card {
  margin-bottom: 20px;
}

.chat-card {
  margin-bottom: 20px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.chat-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 15px;
}

.chat-message {
  margin-bottom: 15px;
}

.user-message, .ai-message {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.user-message {
  justify-content: flex-end;
}

.user-message .message-content {
  background-color: #409eff;
  color: white;
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 70%;
  word-wrap: break-word;
}

.ai-message .message-content {
  background-color: white;
  border: 1px solid #e4e7ed;
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 70%;
  word-wrap: break-word;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background-color: #f0f0f0;
  flex-shrink: 0;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
  margin: 10px 0 5px 0;
  color: #303133;
}

.markdown-content p {
  margin: 5px 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.markdown-content th, .markdown-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.markdown-content th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409eff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-area {
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
}

.input-actions {
  margin-top: 10px;
  text-align: right;
}

/* 新增：表格模式样式 */
.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.quick-config {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.schedule-table-container {
  margin-top: 20px;
}

.schedule-cell {
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  font-weight: 500;
}

/* 岗位颜色样式 */
.position-responsibility {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1976d2;
}

.position-duty {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #d32f2f;
}

.position-treatment {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  color: #7b1fa2;
}

.position-night {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  color: #388e3c;
}

.position-standby {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  color: #f57c00;
}

.position-rest {
  background: linear-gradient(135deg, #e0f2f1, #b2dfdb);
  color: #00796b;
}

.position-management {
  background: linear-gradient(135deg, #fce4ec, #f8bbd9);
  color: #c2185b;
}
</style>
